enum PrayerType { fajr, dhuhr, asr, maghrib, isha }

enum PrayerStatus { completed, missed, onTime, late }

class PrayerModel {
  final PrayerType type;
  final DateTime date;
  final PrayerStatus status;
  final DateTime? completedAt;
  
  PrayerModel({
    required this.type,
    required this.date,
    required this.status,
    this.completedAt,
  });
  
  String get name {
    switch (type) {
      case PrayerType.fajr:
        return 'fajr';
      case PrayerType.dhuhr:
        return 'dhuhr';
      case PrayerType.asr:
        return 'asr';
      case PrayerType.maghrib:
        return 'maghrib';
      case PrayerType.isha:
        return 'isha';
    }
  }
  
  Map<String, dynamic> toJson() {
    return {
      'type': type.index,
      'date': date.toIso8601String(),
      'status': status.index,
      'completedAt': completedAt?.toIso8601String(),
    };
  }
  
  factory PrayerModel.fromJson(Map<String, dynamic> json) {
    return PrayerModel(
      type: PrayerType.values[json['type']],
      date: DateTime.parse(json['date']),
      status: PrayerStatus.values[json['status']],
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt'])
          : null,
    );
  }
}
