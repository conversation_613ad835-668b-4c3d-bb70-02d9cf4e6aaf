class UserModel {
  final String name;
  final String gender;
  final DateTime birthDate;
  final DateTime? prayerStartDate;
  
  UserModel({
    required this.name,
    required this.gender,
    required this.birthDate,
    this.prayerStartDate,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'gender': gender,
      'birthDate': birthDate.toIso8601String(),
      'prayerStartDate': prayerStartDate?.toIso8601String(),
    };
  }
  
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      name: json['name'],
      gender: json['gender'],
      birthDate: DateTime.parse(json['birthDate']),
      prayerStartDate: json['prayerStartDate'] != null 
          ? DateTime.parse(json['prayerStartDate'])
          : null,
    );
  }
}
