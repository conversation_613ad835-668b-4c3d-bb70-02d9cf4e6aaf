import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class Progress<PERSON>hart extends StatefulWidget {
  const ProgressChart({Key? key}) : super(key: key);

  @override
  State<ProgressChart> createState() => _ProgressChartState();
}

class _ProgressChartState extends State<ProgressChart> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<Animation<double>> _barAnimations;
  
  // Mock data for the last 7 days
  final List<Map<String, dynamic>> _weekData = [
    {'day': 'السبت', 'completed': 4, 'total': 5},
    {'day': 'الأحد', 'completed': 5, 'total': 5},
    {'day': 'الاثنين', 'completed': 3, 'total': 5},
    {'day': 'الثلاثاء', 'completed': 5, 'total': 5},
    {'day': 'الأربعاء', 'completed': 4, 'total': 5},
    {'day': 'الخميس', 'completed': 5, 'total': 5},
    {'day': 'الجمعة', 'completed': 2, 'total': 5},
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _barAnimations = List.generate(
      _weekData.length,
      (index) => Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          index * 0.1,
          0.6 + (index * 0.1),
          curve: Curves.easeOut,
        ),
      )),
    );
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.primaryPurple.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.bar_chart,
                  color: AppTheme.primaryPurple,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                'تقدم الأسبوع',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 30),
          
          // Chart
          SizedBox(
            height: 200,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: _weekData.asMap().entries.map((entry) {
                final index = entry.key;
                final data = entry.value;
                final percentage = data['completed'] / data['total'];
                
                return AnimatedBuilder(
                  animation: _barAnimations[index],
                  builder: (context, child) {
                    return Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        // Percentage Text
                        Text(
                          '${(percentage * 100).round()}%',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: _getBarColor(percentage),
                          ),
                        ),
                        
                        const SizedBox(height: 8),
                        
                        // Bar
                        Container(
                          width: 30,
                          height: 150 * percentage * _barAnimations[index].value,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.bottomCenter,
                              end: Alignment.topCenter,
                              colors: [
                                _getBarColor(percentage),
                                _getBarColor(percentage).withOpacity(0.7),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(15),
                            boxShadow: [
                              BoxShadow(
                                color: _getBarColor(percentage).withOpacity(0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 8),
                        
                        // Day Label
                        Text(
                          data['day'],
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    );
                  },
                );
              }).toList(),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Legend
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildLegendItem('ممتاز', AppTheme.primaryGreen),
              const SizedBox(width: 20),
              _buildLegendItem('جيد', AppTheme.primaryBlue),
              const SizedBox(width: 20),
              _buildLegendItem('يحتاج تحسين', AppTheme.primaryOrange),
            ],
          ),
        ],
      ),
    );
  }

  Color _getBarColor(double percentage) {
    if (percentage >= 0.8) {
      return AppTheme.primaryGreen;
    } else if (percentage >= 0.6) {
      return AppTheme.primaryBlue;
    } else if (percentage >= 0.4) {
      return AppTheme.primaryOrange;
    } else {
      return AppTheme.primaryRed;
    }
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
}
