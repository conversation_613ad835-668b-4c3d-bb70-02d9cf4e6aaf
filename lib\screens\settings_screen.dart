import 'package:flutter/material.dart';
import '../generated/l10n.dart';
import '../theme/app_theme.dart';
import '../providers/theme_provider.dart';
import '../providers/language_provider.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  final ThemeProvider _themeProvider = ThemeProvider();
  final LanguageProvider _languageProvider = LanguageProvider();
  
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _animationController.forward();
    _loadSettings();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    await _themeProvider.loadTheme();
    await _languageProvider.loadLanguage();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = S.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.settings),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Appearance Section
                _buildSectionHeader('المظهر', Icons.palette),
                _buildSettingsCard([
                  _buildSwitchTile(
                    title: localizations.darkMode,
                    subtitle: 'تفعيل الوضع الليلي',
                    icon: Icons.dark_mode,
                    value: _themeProvider.isDarkMode,
                    onChanged: (value) async {
                      await _themeProvider.toggleTheme();
                      setState(() {});
                    },
                  ),
                  _buildLanguageTile(),
                ]),
                
                const SizedBox(height: 24),
                
                // Notifications Section
                _buildSectionHeader('الإشعارات', Icons.notifications),
                _buildSettingsCard([
                  _buildSwitchTile(
                    title: 'الإشعارات',
                    subtitle: 'تلقي إشعارات أوقات الصلاة',
                    icon: Icons.notifications,
                    value: _notificationsEnabled,
                    onChanged: (value) {
                      setState(() {
                        _notificationsEnabled = value;
                      });
                    },
                  ),
                  _buildSwitchTile(
                    title: 'الصوت',
                    subtitle: 'تشغيل الأصوات',
                    icon: Icons.volume_up,
                    value: _soundEnabled,
                    onChanged: (value) {
                      setState(() {
                        _soundEnabled = value;
                      });
                    },
                  ),
                  _buildSwitchTile(
                    title: 'الاهتزاز',
                    subtitle: 'تفعيل الاهتزاز مع الإشعارات',
                    icon: Icons.vibration,
                    value: _vibrationEnabled,
                    onChanged: (value) {
                      setState(() {
                        _vibrationEnabled = value;
                      });
                    },
                  ),
                ]),
                
                const SizedBox(height: 24),
                
                // Prayer Settings Section
                _buildSectionHeader('إعدادات الصلاة', Icons.mosque),
                _buildSettingsCard([
                  _buildTile(
                    title: 'طريقة الحساب',
                    subtitle: 'رابطة العالم الإسلامي',
                    icon: Icons.calculate,
                    onTap: () {
                      _showCalculationMethodDialog();
                    },
                  ),
                  _buildTile(
                    title: 'المذهب الفقهي',
                    subtitle: 'شافعي',
                    icon: Icons.book,
                    onTap: () {
                      _showMadhabDialog();
                    },
                  ),
                  _buildTile(
                    title: 'الموقع',
                    subtitle: 'الرياض، السعودية',
                    icon: Icons.location_on,
                    onTap: () {
                      // Handle location settings
                    },
                  ),
                ]),
                
                const SizedBox(height: 24),
                
                // Data & Privacy Section
                _buildSectionHeader('البيانات والخصوصية', Icons.security),
                _buildSettingsCard([
                  _buildTile(
                    title: 'نسخ احتياطي',
                    subtitle: 'حفظ البيانات في السحابة',
                    icon: Icons.cloud_upload,
                    onTap: () {
                      // Handle backup
                    },
                  ),
                  _buildTile(
                    title: 'استعادة البيانات',
                    subtitle: 'استعادة من النسخة الاحتياطية',
                    icon: Icons.cloud_download,
                    onTap: () {
                      // Handle restore
                    },
                  ),
                  _buildTile(
                    title: 'مسح البيانات',
                    subtitle: 'حذف جميع البيانات المحلية',
                    icon: Icons.delete_forever,
                    onTap: () {
                      _showDeleteDataDialog();
                    },
                    isDestructive: true,
                  ),
                ]),
                
                const SizedBox(height: 24),
                
                // About Section
                _buildSectionHeader('حول التطبيق', Icons.info),
                _buildSettingsCard([
                  _buildTile(
                    title: 'الإصدار',
                    subtitle: '1.0.0',
                    icon: Icons.info,
                    onTap: () {
                      // Show version info
                    },
                  ),
                  _buildTile(
                    title: 'شروط الاستخدام',
                    subtitle: 'اقرأ شروط الاستخدام',
                    icon: Icons.description,
                    onTap: () {
                      // Show terms
                    },
                  ),
                  _buildTile(
                    title: 'سياسة الخصوصية',
                    subtitle: 'اقرأ سياسة الخصوصية',
                    icon: Icons.privacy_tip,
                    onTap: () {
                      // Show privacy policy
                    },
                  ),
                ]),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppTheme.primaryGreen,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryGreen,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsCard(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: children,
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppTheme.primaryGreen.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: AppTheme.primaryGreen,
          size: 20,
        ),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppTheme.primaryGreen,
      ),
    );
  }

  Widget _buildTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: (isDestructive ? AppTheme.primaryRed : AppTheme.primaryGreen).withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: isDestructive ? AppTheme.primaryRed : AppTheme.primaryGreen,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isDestructive ? AppTheme.primaryRed : null,
        ),
      ),
      subtitle: Text(subtitle),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Colors.grey[400],
      ),
      onTap: onTap,
    );
  }

  Widget _buildLanguageTile() {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppTheme.primaryBlue.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.language,
          color: AppTheme.primaryBlue,
          size: 20,
        ),
      ),
      title: Text(S.of(context).language),
      subtitle: Text(_languageProvider.isArabic ? 'العربية' : 'English'),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Colors.grey[400],
      ),
      onTap: () {
        _showLanguageDialog();
      },
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(S.of(context).language),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('العربية'),
              value: 'ar',
              groupValue: _languageProvider.locale.languageCode,
              onChanged: (value) async {
                if (value != null) {
                  await _languageProvider.changeLanguage(value);
                  setState(() {});
                  Navigator.pop(context);
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('English'),
              value: 'en',
              groupValue: _languageProvider.locale.languageCode,
              onChanged: (value) async {
                if (value != null) {
                  await _languageProvider.changeLanguage(value);
                  setState(() {});
                  Navigator.pop(context);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showCalculationMethodDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('طريقة الحساب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            'رابطة العالم الإسلامي',
            'الهيئة العامة المصرية للمساحة',
            'جامعة أم القرى',
            'الاتحاد الإسلامي لأمريكا الشمالية',
          ].map((method) => ListTile(
            title: Text(method),
            onTap: () {
              Navigator.pop(context);
            },
          )).toList(),
        ),
      ),
    );
  }

  void _showMadhabDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('المذهب الفقهي'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            'شافعي',
            'حنفي',
            'مالكي',
            'حنبلي',
          ].map((madhab) => ListTile(
            title: Text(madhab),
            onTap: () {
              Navigator.pop(context);
            },
          )).toList(),
        ),
      ),
    );
  }

  void _showDeleteDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحذير'),
        content: const Text(
          'هل أنت متأكد من رغبتك في حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Handle data deletion
            },
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.primaryRed,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
