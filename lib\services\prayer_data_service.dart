import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/prayer_times_model.dart';
import '../models/user_model.dart';

/// خدمة إدارة بيانات الصلاة والحفظ المحلي
class PrayerDataService {
  static const String _prayerDataKey = 'prayer_data';
  static const String _userDataKey = 'user_data';

  /// حفظ بيانات الصلاة لتاريخ معين
  static Future<void> saveDailyPrayerStatus(DailyPrayerStatus status) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // جلب البيانات الحالية
      final existingData = await getAllPrayerData();

      // إضافة أو تحديث بيانات اليوم
      final dateKey = _formatDateKey(status.date);
      existingData[dateKey] = status.toJson();

      // حفظ البيانات المحدثة
      await prefs.setString(_prayerDataKey, json.encode(existingData));

      print('✅ تم حفظ بيانات الصلاة لتاريخ: $dateKey');
    } catch (e) {
      print('❌ خطأ في حفظ بيانات الصلاة: $e');
    }
  }

  /// جلب بيانات الصلاة لتاريخ معين
  static Future<DailyPrayerStatus?> getDailyPrayerStatus(DateTime date) async {
    try {
      final allData = await getAllPrayerData();
      final dateKey = _formatDateKey(date);

      if (allData.containsKey(dateKey)) {
        return DailyPrayerStatus.fromJson(allData[dateKey]);
      }

      return null;
    } catch (e) {
      print('❌ خطأ في جلب بيانات الصلاة: $e');
      return null;
    }
  }

  /// جلب جميع بيانات الصلاة
  static Future<Map<String, dynamic>> getAllPrayerData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataString = prefs.getString(_prayerDataKey);

      if (dataString != null) {
        return Map<String, dynamic>.from(json.decode(dataString));
      }

      return {};
    } catch (e) {
      print('❌ خطأ في جلب جميع بيانات الصلاة: $e');
      return {};
    }
  }

  /// تحديث حالة صلاة معينة
  static Future<void> updatePrayerStatus({
    required DateTime date,
    required String prayerName,
    required bool completed,
  }) async {
    try {
      // جلب بيانات اليوم الحالية
      DailyPrayerStatus? currentStatus = await getDailyPrayerStatus(date);

      // إذا لم توجد بيانات لهذا اليوم، إنشاء بيانات جديدة
      if (currentStatus == null) {
        final hijriDate =
            '${date.day} ${_getMonthName(date.month)} ${date.year} م';
        currentStatus = DailyPrayerStatus.newDay(
          date: date,
          hijriDate: hijriDate,
        );
      }

      // تحديث حالة الصلاة
      final updatedStatus =
          currentStatus.copyWithPrayerStatus(prayerName, completed);

      // حفظ البيانات المحدثة
      await saveDailyPrayerStatus(updatedStatus);

      print(
          '✅ تم تحديث حالة صلاة $prayerName لتاريخ ${_formatDateKey(date)}: $completed');
    } catch (e) {
      print('❌ خطأ في تحديث حالة الصلاة: $e');
    }
  }

  /// إنشاء بيانات الصلاة من سن التكليف إلى الآن
  static Future<void> initializePrayerDataFromMaturity() async {
    try {
      final userData = await getUserData();
      if (userData == null) return;

      // تحديد تاريخ بداية التكليف (15 سنة هجرية)
      final birthDate = userData.birthDate;
      final maturityDate = DateTime(
        birthDate.year + 15,
        birthDate.month,
        birthDate.day,
      );

      // تحديد تاريخ بداية الصلاة (إذا حدده المستخدم)
      final prayerStartDate = userData.prayerStartDate ?? maturityDate;

      final now = DateTime.now();
      final allData = await getAllPrayerData();

      // إنشاء بيانات لكل يوم من سن التكليف إلى الآن
      DateTime currentDate = maturityDate;

      while (currentDate.isBefore(now) || currentDate.isAtSameMomentAs(now)) {
        final dateKey = _formatDateKey(currentDate);

        // إذا لم توجد بيانات لهذا اليوم، إنشاؤها
        if (!allData.containsKey(dateKey)) {
          final hijriDate =
              '${currentDate.day} ${_getMonthName(currentDate.month)} ${currentDate.year} م';

          // تحديد الحالة الافتراضية
          bool defaultCompleted = false;

          // إذا كان التاريخ قبل بداية الصلاة، اعتبر الصلوات مكتملة
          if (currentDate.isBefore(prayerStartDate)) {
            defaultCompleted = true;
          }
          // إذا كان التاريخ بعد بداية الصلاة وقبل اليوم الحالي، اعتبر الصلوات مكتملة
          else if (currentDate
              .isBefore(DateTime.now().subtract(const Duration(days: 1)))) {
            defaultCompleted = true;
          }

          final dailyStatus = DailyPrayerStatus.newDay(
            date: currentDate,
            hijriDate: hijriDate,
            defaultCompleted: defaultCompleted,
          );

          allData[dateKey] = dailyStatus.toJson();
        }

        // الانتقال لليوم التالي
        currentDate = currentDate.add(const Duration(days: 1));
      }

      // حفظ جميع البيانات
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_prayerDataKey, json.encode(allData));

      print('✅ تم تهيئة بيانات الصلاة من سن التكليف إلى الآن');
    } catch (e) {
      print('❌ خطأ في تهيئة بيانات الصلاة: $e');
    }
  }

  /// حساب إحصائيات الصلاة
  static Future<Map<String, dynamic>> calculatePrayerStatistics() async {
    try {
      final allData = await getAllPrayerData();

      int totalDays = 0;
      int completedDays = 0;
      Map<String, int> prayerCounts = {
        'fajr_completed': 0,
        'fajr_missed': 0,
        'dhuhr_completed': 0,
        'dhuhr_missed': 0,
        'asr_completed': 0,
        'asr_missed': 0,
        'maghrib_completed': 0,
        'maghrib_missed': 0,
        'isha_completed': 0,
        'isha_missed': 0,
      };

      for (final entry in allData.entries) {
        final dailyStatus = DailyPrayerStatus.fromJson(entry.value);
        totalDays++;

        if (dailyStatus.isAllPrayersCompleted) {
          completedDays++;
        }

        // حساب كل صلاة
        for (final prayerEntry in dailyStatus.prayerStatus.entries) {
          final prayerName = prayerEntry.key;
          final isCompleted = prayerEntry.value;

          if (isCompleted) {
            prayerCounts['${prayerName}_completed'] =
                (prayerCounts['${prayerName}_completed'] ?? 0) + 1;
          } else {
            prayerCounts['${prayerName}_missed'] =
                (prayerCounts['${prayerName}_missed'] ?? 0) + 1;
          }
        }
      }

      return {
        'totalDays': totalDays,
        'completedDays': completedDays,
        'incompleteDays': totalDays - completedDays,
        'completionRate': totalDays > 0 ? (completedDays / totalDays) * 100 : 0,
        'prayerCounts': prayerCounts,
      };
    } catch (e) {
      print('❌ خطأ في حساب إحصائيات الصلاة: $e');
      return {};
    }
  }

  /// حفظ بيانات المستخدم
  static Future<void> saveUserData(UserModel user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userDataKey, json.encode(user.toJson()));
      print('✅ تم حفظ بيانات المستخدم');
    } catch (e) {
      print('❌ خطأ في حفظ بيانات المستخدم: $e');
    }
  }

  /// جلب بيانات المستخدم
  static Future<UserModel?> getUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userDataString = prefs.getString(_userDataKey);

      if (userDataString != null) {
        final userData = json.decode(userDataString);
        return UserModel.fromJson(userData);
      }

      return null;
    } catch (e) {
      print('❌ خطأ في جلب بيانات المستخدم: $e');
      return null;
    }
  }

  /// تنسيق مفتاح التاريخ
  static String _formatDateKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// الحصول على اسم الشهر بالعربية
  static String _getMonthName(int month) {
    const months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر'
    ];
    return months[month - 1];
  }

  /// حساب السلسلة الحالية (الأيام المتتالية المكتملة)
  static Future<int> calculateCurrentStreak() async {
    try {
      final allData = await getAllPrayerData();
      final sortedDates = allData.keys.toList()..sort();

      int streak = 0;
      final today = DateTime.now();

      // البحث من اليوم الحالي إلى الوراء
      for (int i = sortedDates.length - 1; i >= 0; i--) {
        final dateKey = sortedDates[i];
        final date = DateTime.parse(dateKey);

        // تجاهل التواريخ المستقبلية
        if (date.isAfter(today)) continue;

        final dailyStatus = DailyPrayerStatus.fromJson(allData[dateKey]);

        if (dailyStatus.isAllPrayersCompleted) {
          streak++;
        } else {
          break; // انقطاع السلسلة
        }
      }

      return streak;
    } catch (e) {
      print('❌ خطأ في حساب السلسلة: $e');
      return 0;
    }
  }
}
