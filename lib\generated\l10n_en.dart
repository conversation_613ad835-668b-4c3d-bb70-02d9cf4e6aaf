import 'l10n.dart';

/// The translations for English (`en`).
class SEn extends S {
  SEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Salaty';

  @override
  String get login => 'Login';

  @override
  String get name => 'Name';

  @override
  String get gender => 'Gender';

  @override
  String get male => 'Male';

  @override
  String get female => 'Female';

  @override
  String get birthDate => 'Birth Date';

  @override
  String get whenDidYouStartPraying => 'When did you start praying?';

  @override
  String get optional => 'Optional';

  @override
  String get getStarted => 'Get Started';

  @override
  String get myPrayers => 'My Prayers';

  @override
  String get prayerProgress => 'Prayer Progress';

  @override
  String get myProgress => 'My Progress';

  @override
  String get holyQuran => 'Holy Quran';

  @override
  String get profile => 'Profile';

  @override
  String get settings => 'Settings';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get language => 'Language';

  @override
  String get arabic => 'Arabic';

  @override
  String get english => 'English';

  @override
  String get fajr => 'Fajr';

  @override
  String get dhuhr => 'Dhuhr';

  @override
  String get asr => 'Asr';

  @override
  String get maghrib => 'Maghrib';

  @override
  String get isha => 'Isha';

  @override
  String get completed => 'Completed';

  @override
  String get missed => 'Missed';

  @override
  String get onTime => 'On Time';

  @override
  String get late => 'Late';

  @override
  String get streak => 'Streak';

  @override
  String get days => 'Days';

  @override
  String get thisWeek => 'This Week';

  @override
  String get thisMonth => 'This Month';

  @override
  String get allTime => 'All Time';

  @override
  String get prayerTimes => 'Prayer Times';

  @override
  String get nextPrayer => 'Next Prayer';

  @override
  String get timeRemaining => 'Time Remaining';
}
