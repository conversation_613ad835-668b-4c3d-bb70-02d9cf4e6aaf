import 'package:flutter/material.dart';
import 'package:animations/animations.dart';
import '../generated/l10n.dart';
import '../theme/app_theme.dart';
import '../models/prayer_model.dart';
import '../widgets/prayer_card.dart';
import '../widgets/prayer_times_card.dart';
import '../widgets/streak_card.dart';
import '../widgets/next_prayer_card.dart';

class PrayersScreen extends StatefulWidget {
  const PrayersScreen({Key? key}) : super(key: key);

  @override
  State<PrayersScreen> createState() => _PrayersScreenState();
}

class _PrayersScreenState extends State<PrayersScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<Animation<double>> _cardAnimations;
  
  final List<PrayerModel> _todayPrayers = [
    PrayerModel(
      type: PrayerType.fajr,
      date: DateTime.now(),
      status: PrayerStatus.completed,
      completedAt: DateTime.now().subtract(const Duration(hours: 6)),
    ),
    PrayerModel(
      type: PrayerType.dhuhr,
      date: DateTime.now(),
      status: PrayerStatus.onTime,
      completedAt: DateTime.now().subtract(const Duration(hours: 2)),
    ),
    PrayerModel(
      type: PrayerType.asr,
      date: DateTime.now(),
      status: PrayerStatus.late,
    ),
    PrayerModel(
      type: PrayerType.maghrib,
      date: DateTime.now(),
      status: PrayerStatus.missed,
    ),
    PrayerModel(
      type: PrayerType.isha,
      date: DateTime.now(),
      status: PrayerStatus.completed,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _cardAnimations = List.generate(
      6,
      (index) => Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          index * 0.1,
          0.6 + (index * 0.1),
          curve: Curves.easeOut,
        ),
      )),
    );
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = S.of(context);
    final theme = Theme.of(context);
    
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          await Future.delayed(const Duration(seconds: 1));
          _animationController.reset();
          _animationController.forward();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome Message
              FadeTransition(
                opacity: _cardAnimations[0],
                child: SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0, 0.3),
                    end: Offset.zero,
                  ).animate(_cardAnimations[0]),
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppTheme.primaryGreen,
                          AppTheme.primaryGreen.withOpacity(0.8),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.primaryGreen.withOpacity(0.3),
                          blurRadius: 15,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(
                              Icons.wb_sunny,
                              color: Colors.white,
                              size: 28,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              _getGreeting(),
                              style: theme.textTheme.headlineMedium?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'استمر في رحلتك الروحانية',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: Colors.white.withOpacity(0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Next Prayer Card
              FadeTransition(
                opacity: _cardAnimations[1],
                child: SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(-0.3, 0),
                    end: Offset.zero,
                  ).animate(_cardAnimations[1]),
                  child: const NextPrayerCard(),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Streak Card
              FadeTransition(
                opacity: _cardAnimations[2],
                child: SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0.3, 0),
                    end: Offset.zero,
                  ).animate(_cardAnimations[2]),
                  child: const StreakCard(),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Prayer Times Card
              FadeTransition(
                opacity: _cardAnimations[3],
                child: SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0, 0.3),
                    end: Offset.zero,
                  ).animate(_cardAnimations[3]),
                  child: const PrayerTimesCard(),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Today's Prayers Section
              FadeTransition(
                opacity: _cardAnimations[4],
                child: Text(
                  'صلوات اليوم',
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Prayer Cards
              FadeTransition(
                opacity: _cardAnimations[5],
                child: ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _todayPrayers.length,
                  separatorBuilder: (context, index) => const SizedBox(height: 12),
                  itemBuilder: (context, index) {
                    return SlideTransition(
                      position: Tween<Offset>(
                        begin: Offset(0.3, 0),
                        end: Offset.zero,
                      ).animate(CurvedAnimation(
                        parent: _animationController,
                        curve: Interval(
                          0.6 + (index * 0.05),
                          0.8 + (index * 0.05),
                          curve: Curves.easeOut,
                        ),
                      )),
                      child: PrayerCard(
                        prayer: _todayPrayers[index],
                        onStatusChanged: (status) {
                          setState(() {
                            _todayPrayers[index] = PrayerModel(
                              type: _todayPrayers[index].type,
                              date: _todayPrayers[index].date,
                              status: status,
                              completedAt: status == PrayerStatus.completed 
                                  ? DateTime.now() 
                                  : null,
                            );
                          });
                        },
                      ),
                    );
                  },
                ),
              ),
              
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'صباح الخير';
    } else if (hour < 17) {
      return 'مساء الخير';
    } else {
      return 'مساء الخير';
    }
  }
}
