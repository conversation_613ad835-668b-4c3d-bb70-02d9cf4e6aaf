# تطبيق صلاتي - Salaty App

تطبيق صلاتي هو تطبيق Flutter مصمم بتصميم مشابه لتطبيق Duolingo لمساعدة المسلمين في تتبع صلواتهم اليومية وتحسين علاقتهم الروحانية.

## الميزات الرئيسية

### 🕌 تتبع الصلوات
- تتبع الصلوات الخمس اليومية
- إمكانية تسجيل حالة كل صلاة (مكتملة، فائتة، في الوقت، متأخرة)
- عرض أوقات الصلاة
- تنبيهات للصلاة التالية

### 📊 إحصائيات وتقدم
- عرض تقدم الصلوات الأسبوعي والشهري
- إحصائيات مفصلة لكل صلاة
- نظام السلسلة (Streak) للأيام المتتالية
- رسوم بيانية تفاعلية

### 📖 القرآن الكريم
- قائمة بجميع سور القرآن الكريم
- معلومات عن كل سورة (عدد الآيات، مكية/مدنية)
- إمكانية البحث في السور

### 🎨 تصميم جذاب
- تصميم مستوحى من Duolingo
- ألوان زاهية وانتقالات سلسة
- واجهة مستخدم بديهية وسهلة الاستخدام

### 🌐 دعم متعدد اللغات
- دعم اللغة العربية والإنجليزية
- إمكانية التبديل بين اللغات
- دعم الكتابة من اليمين إلى اليسار (RTL)

### 🌙 الوضع الليلي
- دعم الوضع الليلي والنهاري
- تبديل تلقائي أو يدوي
- ألوان محسنة للعينين

## هيكل التطبيق

### الصفحات الرئيسية
1. **صفحة تسجيل الدخول**: جمع المعلومات الأساسية للمستخدم
2. **صلاتي**: الصفحة الرئيسية لتتبع الصلوات اليومية
3. **تقدم صلاتي**: إحصائيات وتقدم الصلوات
4. **تقدمي**: الإنجازات والأهداف الشخصية
5. **القرآن الكريم**: تصفح سور القرآن الكريم

### القائمة الجانبية (Drawer)
- الملف الشخصي
- الإعدادات
- المساعدة
- حول التطبيق

## التقنيات المستخدمة

- **Flutter**: إطار العمل الرئيسي
- **Dart**: لغة البرمجة
- **Material Design 3**: نظام التصميم
- **Google Fonts**: خطوط مخصصة
- **SharedPreferences**: حفظ البيانات محلياً
- **Animations**: انتقالات وحركات سلسة

## كيفية التشغيل

### المتطلبات
- Flutter SDK (3.0.0 أو أحدث)
- Dart SDK
- Android Studio أو VS Code
- جهاز Android أو محاكي

### خطوات التشغيل

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd salaty_app
```

2. **تثبيت التبعيات**
```bash
flutter pub get
```

3. **تشغيل التطبيق**
```bash
flutter run
```

### بناء التطبيق للإنتاج

```bash
# بناء APK
flutter build apk --release

# بناء App Bundle
flutter build appbundle --release
```

## هيكل المجلدات

```
lib/
├── generated/          # ملفات الترجمة المولدة
├── l10n/              # ملفات الترجمة
├── models/            # نماذج البيانات
├── providers/         # مزودي الحالة
├── screens/           # شاشات التطبيق
├── theme/             # إعدادات التصميم
├── widgets/           # المكونات المخصصة
└── main.dart          # نقطة البداية
```

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى فتح Issue في المستودع.

---

**ملاحظة**: هذا التطبيق في مرحلة التطوير ويحتوي على بيانات وهمية لأغراض العرض. في النسخة الإنتاجية، ستحتاج إلى دمج APIs حقيقية لأوقات الصلاة وقاعدة بيانات لحفظ بيانات المستخدمين.
