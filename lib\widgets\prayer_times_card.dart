import 'package:flutter/material.dart';
import '../generated/l10n.dart';
import '../theme/app_theme.dart';
import '../models/prayer_model.dart';

class PrayerTimesCard extends StatefulWidget {
  const PrayerTimesCard({Key? key}) : super(key: key);

  @override
  State<PrayerTimesCard> createState() => _PrayerTimesCardState();
}

class _PrayerTimesCardState extends State<PrayerTimesCard> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<Animation<double>> _itemAnimations;
  
  // Mock prayer times - في التطبيق الحقيقي ستأتي من API
  final Map<PrayerType, TimeOfDay> _prayerTimes = {
    PrayerType.fajr: const TimeOfDay(hour: 5, minute: 30),
    PrayerType.dhuhr: const TimeOfDay(hour: 12, minute: 15),
    PrayerType.asr: const TimeOfDay(hour: 15, minute: 45),
    PrayerType.maghrib: const TimeOfDay(hour: 18, minute: 20),
    PrayerType.isha: const TimeOfDay(hour: 19, minute: 45),
  };

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _itemAnimations = List.generate(
      _prayerTimes.length,
      (index) => Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          index * 0.1,
          0.6 + (index * 0.1),
          curve: Curves.easeOut,
        ),
      )),
    );
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  String _getPrayerName(PrayerType type, S localizations) {
    switch (type) {
      case PrayerType.fajr:
        return localizations.fajr;
      case PrayerType.dhuhr:
        return localizations.dhuhr;
      case PrayerType.asr:
        return localizations.asr;
      case PrayerType.maghrib:
        return localizations.maghrib;
      case PrayerType.isha:
        return localizations.isha;
    }
  }

  Color _getPrayerColor(PrayerType type) {
    switch (type) {
      case PrayerType.fajr:
        return AppTheme.primaryBlue;
      case PrayerType.dhuhr:
        return AppTheme.primaryOrange;
      case PrayerType.asr:
        return AppTheme.primaryGreen;
      case PrayerType.maghrib:
        return AppTheme.primaryPurple;
      case PrayerType.isha:
        return AppTheme.primaryRed;
    }
  }

  IconData _getPrayerIcon(PrayerType type) {
    switch (type) {
      case PrayerType.fajr:
        return Icons.wb_twilight;
      case PrayerType.dhuhr:
        return Icons.wb_sunny;
      case PrayerType.asr:
        return Icons.wb_sunny_outlined;
      case PrayerType.maghrib:
        return Icons.wb_twilight;
      case PrayerType.isha:
        return Icons.nightlight_round;
    }
  }

  bool _isCurrentPrayer(PrayerType type) {
    final now = TimeOfDay.now();
    final currentMinutes = now.hour * 60 + now.minute;
    final prayerMinutes = _prayerTimes[type]!.hour * 60 + _prayerTimes[type]!.minute;
    
    // تحديد الصلاة الحالية بناءً على الوقت
    const prayerTypes = PrayerType.values;
    final currentIndex = prayerTypes.indexOf(type);
    
    if (currentIndex == 0) {
      // الفجر
      return currentMinutes >= prayerMinutes && currentMinutes < (_prayerTimes[prayerTypes[1]]!.hour * 60 + _prayerTimes[prayerTypes[1]]!.minute);
    } else if (currentIndex == prayerTypes.length - 1) {
      // العشاء
      return currentMinutes >= prayerMinutes;
    } else {
      // باقي الصلوات
      final nextPrayerMinutes = _prayerTimes[prayerTypes[currentIndex + 1]]!.hour * 60 + _prayerTimes[prayerTypes[currentIndex + 1]]!.minute;
      return currentMinutes >= prayerMinutes && currentMinutes < nextPrayerMinutes;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = S.of(context);
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.primaryOrange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.schedule,
                  color: AppTheme.primaryOrange,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                localizations.prayerTimes,
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          Column(
            children: _prayerTimes.entries.map((entry) {
              final index = _prayerTimes.keys.toList().indexOf(entry.key);
              final isCurrentPrayer = _isCurrentPrayer(entry.key);
              
              return AnimatedBuilder(
                animation: _itemAnimations[index],
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(
                      (1 - _itemAnimations[index].value) * 100,
                      0,
                    ),
                    child: Opacity(
                      opacity: _itemAnimations[index].value,
                      child: Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: isCurrentPrayer 
                              ? _getPrayerColor(entry.key).withOpacity(0.1)
                              : Colors.grey.withOpacity(0.05),
                          borderRadius: BorderRadius.circular(12),
                          border: isCurrentPrayer
                              ? Border.all(
                                  color: _getPrayerColor(entry.key),
                                  width: 2,
                                )
                              : null,
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: _getPrayerColor(entry.key).withOpacity(0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                _getPrayerIcon(entry.key),
                                color: _getPrayerColor(entry.key),
                                size: 20,
                              ),
                            ),
                            
                            const SizedBox(width: 16),
                            
                            Expanded(
                              child: Text(
                                _getPrayerName(entry.key, localizations),
                                style: theme.textTheme.bodyLarge?.copyWith(
                                  fontWeight: isCurrentPrayer ? FontWeight.bold : FontWeight.normal,
                                  color: isCurrentPrayer ? _getPrayerColor(entry.key) : null,
                                ),
                              ),
                            ),
                            
                            Text(
                              entry.value.format(context),
                              style: theme.textTheme.bodyLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: isCurrentPrayer ? _getPrayerColor(entry.key) : null,
                              ),
                            ),
                            
                            if (isCurrentPrayer) ...[
                              const SizedBox(width: 8),
                              Container(
                                width: 8,
                                height: 8,
                                decoration: BoxDecoration(
                                  color: _getPrayerColor(entry.key),
                                  shape: BoxShape.circle,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
