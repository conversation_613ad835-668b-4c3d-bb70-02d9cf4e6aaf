import 'l10n.dart';

/// The translations for Arabic (`ar`).
class SAr extends S {
  SAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'صلاتي';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get name => 'الاسم';

  @override
  String get gender => 'الجنس';

  @override
  String get male => 'ذكر';

  @override
  String get female => 'أنثى';

  @override
  String get birthDate => 'تاريخ الميلاد';

  @override
  String get whenDidYouStartPraying => 'متى بدأت الصلاة؟';

  @override
  String get optional => 'اختياري';

  @override
  String get getStarted => 'ابدأ الآن';

  @override
  String get myPrayers => 'صلاتي';

  @override
  String get prayerProgress => 'تقدم صلاتي';

  @override
  String get myProgress => 'تقدمي';

  @override
  String get holyQuran => 'القرآن الكريم';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get settings => 'الإعدادات';

  @override
  String get darkMode => 'الوضع الليلي';

  @override
  String get language => 'اللغة';

  @override
  String get arabic => 'العربية';

  @override
  String get english => 'الإنجليزية';

  @override
  String get fajr => 'الفجر';

  @override
  String get dhuhr => 'الظهر';

  @override
  String get asr => 'العصر';

  @override
  String get maghrib => 'المغرب';

  @override
  String get isha => 'العشاء';

  @override
  String get completed => 'مكتملة';

  @override
  String get missed => 'فائتة';

  @override
  String get onTime => 'في الوقت';

  @override
  String get late => 'متأخرة';

  @override
  String get streak => 'السلسلة';

  @override
  String get days => 'أيام';

  @override
  String get thisWeek => 'هذا الأسبوع';

  @override
  String get thisMonth => 'هذا الشهر';

  @override
  String get allTime => 'كل الوقت';

  @override
  String get prayerTimes => 'أوقات الصلاة';

  @override
  String get nextPrayer => 'الصلاة التالية';

  @override
  String get timeRemaining => 'الوقت المتبقي';
}
