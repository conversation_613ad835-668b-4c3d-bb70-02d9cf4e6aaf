import 'dart:convert';

/// نموذج بيانات أوقات الصلاة من API الكفيل
class PrayerTimesModel {
  final String fajr;      // وقت صلاة الفجر
  final String sunrise;   // وقت الشروق
  final String dhuhr;     // وقت صلاة الظهر
  final String sunset;    // وقت الغروب
  final String maghrib;   // وقت صلاة المغرب
  final String hijriDate; // التاريخ الهجري
  final String poweredBy; // مصدر البيانات

  PrayerTimesModel({
    required this.fajr,
    required this.sunrise,
    required this.dhuhr,
    required this.sunset,
    required this.maghrib,
    required this.hijriDate,
    required this.poweredBy,
  });

  /// إنشاء كائن من JSON
  factory PrayerTimesModel.fromJson(Map<String, dynamic> json) {
    return PrayerTimesModel(
      fajr: json['fajir']?.toString().trim() ?? '',
      sunrise: json['sunrise']?.toString().trim() ?? '',
      dhuhr: json['doher']?.toString().trim() ?? '',
      sunset: json['sunset']?.toString().trim() ?? '',
      maghrib: json['maghrib']?.toString().trim() ?? '',
      hijriDate: json['date']?.toString().trim() ?? '',
      poweredBy: json['powerdby']?.toString().trim() ?? '',
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'fajir': fajr,
      'sunrise': sunrise,
      'doher': dhuhr,
      'sunset': sunset,
      'maghrib': maghrib,
      'date': hijriDate,
      'powerdby': poweredBy,
    };
  }

  /// تحويل إلى String
  @override
  String toString() {
    return 'PrayerTimesModel(fajr: $fajr, sunrise: $sunrise, dhuhr: $dhuhr, sunset: $sunset, maghrib: $maghrib, hijriDate: $hijriDate)';
  }

  /// نسخ الكائن مع تعديل بعض القيم
  PrayerTimesModel copyWith({
    String? fajr,
    String? sunrise,
    String? dhuhr,
    String? sunset,
    String? maghrib,
    String? hijriDate,
    String? poweredBy,
  }) {
    return PrayerTimesModel(
      fajr: fajr ?? this.fajr,
      sunrise: sunrise ?? this.sunrise,
      dhuhr: dhuhr ?? this.dhuhr,
      sunset: sunset ?? this.sunset,
      maghrib: maghrib ?? this.maghrib,
      hijriDate: hijriDate ?? this.hijriDate,
      poweredBy: poweredBy ?? this.poweredBy,
    );
  }
}

/// نموذج بيانات الصلاة اليومية مع حالة الإكمال
class DailyPrayerStatus {
  final DateTime date;           // تاريخ اليوم
  final String hijriDate;       // التاريخ الهجري
  final Map<String, bool> prayerStatus; // حالة كل صلاة (مكتملة أم لا)
  final PrayerTimesModel? prayerTimes;   // أوقات الصلاة

  DailyPrayerStatus({
    required this.date,
    required this.hijriDate,
    required this.prayerStatus,
    this.prayerTimes,
  });

  /// إنشاء يوم جديد بحالة افتراضية
  factory DailyPrayerStatus.newDay({
    required DateTime date,
    required String hijriDate,
    PrayerTimesModel? prayerTimes,
    bool defaultCompleted = false,
  }) {
    return DailyPrayerStatus(
      date: date,
      hijriDate: hijriDate,
      prayerTimes: prayerTimes,
      prayerStatus: {
        'fajr': defaultCompleted,
        'dhuhr': defaultCompleted,
        'asr': defaultCompleted,
        'maghrib': defaultCompleted,
        'isha': defaultCompleted,
      },
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'hijriDate': hijriDate,
      'prayerStatus': prayerStatus,
      'prayerTimes': prayerTimes?.toJson(),
    };
  }

  /// إنشاء من JSON
  factory DailyPrayerStatus.fromJson(Map<String, dynamic> json) {
    return DailyPrayerStatus(
      date: DateTime.parse(json['date']),
      hijriDate: json['hijriDate'] ?? '',
      prayerStatus: Map<String, bool>.from(json['prayerStatus'] ?? {}),
      prayerTimes: json['prayerTimes'] != null 
          ? PrayerTimesModel.fromJson(json['prayerTimes'])
          : null,
    );
  }

  /// نسخ مع تعديل حالة صلاة معينة
  DailyPrayerStatus copyWithPrayerStatus(String prayer, bool completed) {
    final newStatus = Map<String, bool>.from(prayerStatus);
    newStatus[prayer] = completed;
    
    return DailyPrayerStatus(
      date: date,
      hijriDate: hijriDate,
      prayerStatus: newStatus,
      prayerTimes: prayerTimes,
    );
  }

  /// حساب عدد الصلوات المكتملة
  int get completedPrayersCount {
    return prayerStatus.values.where((completed) => completed).length;
  }

  /// حساب عدد الصلوات غير المكتملة
  int get incompletePrayersCount {
    return prayerStatus.values.where((completed) => !completed).length;
  }

  /// التحقق من اكتمال جميع صلوات اليوم
  bool get isAllPrayersCompleted {
    return prayerStatus.values.every((completed) => completed);
  }
}
