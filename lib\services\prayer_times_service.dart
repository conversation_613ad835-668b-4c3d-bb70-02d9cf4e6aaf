import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';
import '../models/prayer_times_model.dart';

/// خدمة جلب أوقات الصلاة من API الكفيل للمذهب الشيعي
class PrayerTimesService {
  static const String _baseUrl = 'https://hq.alkafeel.net/Api/init/init.php';

  /// جلب أوقات الصلاة بناءً على الموقع الجغرافي
  static Future<PrayerTimesModel?> getPrayerTimes({
    required double latitude,
    required double longitude,
    required int timezone,
  }) async {
    try {
      // بناء رابط API مع المعاملات المطلوبة
      final url = Uri.parse(
          '$_baseUrl?v=jsonPrayerTimes&timezone=$timezone&long=$longitude&lati=$latitude');

      print('🕌 جاري جلب أوقات الصلاة من: $url');

      // إرسال طلب HTTP
      final response = await http.get(url);

      if (response.statusCode == 200) {
        // فك تشفير البيانات
        final jsonData = json.decode(response.body);
        print('✅ تم جلب أوقات الصلاة بنجاح: $jsonData');

        // تحويل البيانات إلى نموذج
        return PrayerTimesModel.fromJson(jsonData);
      } else {
        print('❌ خطأ في جلب أوقات الصلاة: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('❌ خطأ في الاتصال بـ API: $e');
      return null;
    }
  }

  /// جلب أوقات الصلاة للموقع الحالي
  static Future<PrayerTimesModel?> getPrayerTimesForCurrentLocation() async {
    try {
      // التحقق من صلاحيات الموقع
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          print('❌ تم رفض صلاحيات الموقع');
          // استخدام موقع افتراضي (بغداد)
          return await getPrayerTimes(
            latitude: 33.3152,
            longitude: 44.3661,
            timezone: 3,
          );
        }
      }

      if (permission == LocationPermission.deniedForever) {
        print('❌ صلاحيات الموقع مرفوضة نهائياً');
        // استخدام موقع افتراضي (بغداد)
        return await getPrayerTimes(
          latitude: 33.3152,
          longitude: 44.3661,
          timezone: 3,
        );
      }

      // جلب الموقع الحالي
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      print('📍 الموقع الحالي: ${position.latitude}, ${position.longitude}');

      // جلب أوقات الصلاة للموقع الحالي
      return await getPrayerTimes(
        latitude: position.latitude,
        longitude: position.longitude,
        timezone: 3, // المنطقة الزمنية للعراق
      );
    } catch (e) {
      print('❌ خطأ في جلب الموقع: $e');
      // استخدام موقع افتراضي (بغداد) في حالة الخطأ
      return await getPrayerTimes(
        latitude: 33.3152,
        longitude: 44.3661,
        timezone: 3,
      );
    }
  }

  /// جلب أوقات الصلاة لمدن مختلفة
  static Future<PrayerTimesModel?> getPrayerTimesForCity(
      String cityName) async {
    // إحداثيات بعض المدن العراقية والعربية
    final cityCoordinates = {
      'بغداد': {'lat': 33.3152, 'lng': 44.3661, 'timezone': 3},
      'النجف': {'lat': 32.0000, 'lng': 44.3333, 'timezone': 3},
      'كربلاء': {'lat': 32.6160, 'lng': 44.0242, 'timezone': 3},
      'البصرة': {'lat': 30.5085, 'lng': 47.7804, 'timezone': 3},
      'أربيل': {'lat': 36.1911, 'lng': 44.0093, 'timezone': 3},
      'الموصل': {'lat': 36.3350, 'lng': 43.1189, 'timezone': 3},
      'الرياض': {'lat': 24.7136, 'lng': 46.6753, 'timezone': 3},
      'مكة': {'lat': 21.3891, 'lng': 39.8579, 'timezone': 3},
      'المدينة': {'lat': 24.5247, 'lng': 39.5692, 'timezone': 3},
      'القاهرة': {'lat': 30.0444, 'lng': 31.2357, 'timezone': 2},
      'دمشق': {'lat': 33.5138, 'lng': 36.2765, 'timezone': 3},
      'بيروت': {'lat': 33.8938, 'lng': 35.5018, 'timezone': 3},
    };

    final coordinates = cityCoordinates[cityName];
    if (coordinates != null) {
      return await getPrayerTimes(
        latitude: coordinates['lat']!.toDouble(),
        longitude: coordinates['lng']!.toDouble(),
        timezone: coordinates['timezone']!.toInt(),
      );
    }

    // إذا لم توجد المدينة، استخدم بغداد كافتراضي
    return await getPrayerTimesForCurrentLocation();
  }

  /// تحويل وقت الصلاة من نص إلى TimeOfDay
  static TimeOfDay? parseTimeString(String timeString) {
    try {
      final cleanTime = timeString.trim();
      final parts = cleanTime.split(':');
      if (parts.length == 2) {
        final hour = int.parse(parts[0]);
        final minute = int.parse(parts[1]);
        return TimeOfDay(hour: hour, minute: minute);
      }
    } catch (e) {
      print('❌ خطأ في تحليل الوقت: $timeString');
    }
    return null;
  }

  /// التحقق من وقت الصلاة التالية
  static String getNextPrayerName(PrayerTimesModel prayerTimes) {
    final now = TimeOfDay.now();
    final currentMinutes = now.hour * 60 + now.minute;

    final prayers = [
      {'name': 'الفجر', 'time': parseTimeString(prayerTimes.fajr)},
      {'name': 'الظهر', 'time': parseTimeString(prayerTimes.dhuhr)},
      {'name': 'العصر', 'time': calculateAsrTime(prayerTimes)},
      {'name': 'المغرب', 'time': parseTimeString(prayerTimes.maghrib)},
      {'name': 'العشاء', 'time': calculateIshaTime(prayerTimes)},
    ];

    for (final prayer in prayers) {
      final prayerTime = prayer['time'] as TimeOfDay?;
      if (prayerTime != null) {
        final prayerMinutes = prayerTime.hour * 60 + prayerTime.minute;
        if (prayerMinutes > currentMinutes) {
          return prayer['name'] as String;
        }
      }
    }

    return 'الفجر'; // إذا انتهت صلوات اليوم، الصلاة التالية هي فجر الغد
  }

  /// حساب وقت صلاة العصر (تقريبي)
  static TimeOfDay? calculateAsrTime(PrayerTimesModel prayerTimes) {
    final dhuhrTime = parseTimeString(prayerTimes.dhuhr);
    final sunsetTime = parseTimeString(prayerTimes.sunset);

    if (dhuhrTime != null && sunsetTime != null) {
      final dhuhrMinutes = dhuhrTime.hour * 60 + dhuhrTime.minute;
      final sunsetMinutes = sunsetTime.hour * 60 + sunsetTime.minute;

      // تقدير وقت العصر (تقريباً في منتصف الوقت بين الظهر والغروب + ساعة)
      final asrMinutes =
          dhuhrMinutes + ((sunsetMinutes - dhuhrMinutes) * 0.6).round();

      return TimeOfDay(
        hour: asrMinutes ~/ 60,
        minute: asrMinutes % 60,
      );
    }

    return null;
  }

  /// حساب وقت صلاة العشاء (تقريبي)
  static TimeOfDay? calculateIshaTime(PrayerTimesModel prayerTimes) {
    final maghribTime = parseTimeString(prayerTimes.maghrib);

    if (maghribTime != null) {
      // تقدير وقت العشاء (المغرب + ساعة ونصف تقريباً)
      final maghribMinutes = maghribTime.hour * 60 + maghribTime.minute;
      final ishaMinutes = maghribMinutes + 90; // 90 دقيقة

      return TimeOfDay(
        hour: (ishaMinutes ~/ 60) % 24,
        minute: ishaMinutes % 60,
      );
    }

    return null;
  }
}
