{"aadc_info": {"age_group": 0}, "accessibility": {"captions": {"headless_caption_enabled": false}}, "account_info": [{"access_point": 17, "account_id": "00037FFE1E52532C", "accountcapabilities": {"accountcapabilities/g42tslldmfya": -1, "accountcapabilities/g44tilldmfya": -1, "accountcapabilities/ge2dinbnmnqxa": -1, "accountcapabilities/ge2tkmznmnqxa": -1, "accountcapabilities/ge2tknznmnqxa": -1, "accountcapabilities/ge2tkobnmnqxa": -1, "accountcapabilities/ge3dgmjnmnqxa": -1, "accountcapabilities/ge3dgobnmnqxa": -1, "accountcapabilities/geydgnznmnqxa": -1, "accountcapabilities/geytcnbnmnqxa": -1, "accountcapabilities/gezdcnbnmnqxa": -1, "accountcapabilities/gezdsmbnmnqxa": -1, "accountcapabilities/geztenjnmnqxa": -1, "accountcapabilities/gi2tklldmfya": -1, "accountcapabilities/gu2dqlldmfya": -1, "accountcapabilities/gu4dmlldmfya": -1, "accountcapabilities/guydolldmfya": -1, "accountcapabilities/guzdslldmfya": -1, "accountcapabilities/haytqlldmfya": -1, "accountcapabilities/he4tolldmfya": -1}, "edge_account_age_group": 3, "edge_account_cid": "875a2c0f9ecfa601", "edge_account_environment": 0, "edge_account_environment_string": "login.microsoftonline.com", "edge_account_first_name": "HUSSEIN", "edge_account_is_test_on_premises_profile": false, "edge_account_last_name": "ABDULELAH", "edge_account_location": "IQ", "edge_account_oid": "", "edge_account_phone_number": "", "edge_account_puid": "00037FFE1E52532C", "edge_account_sovereignty": 0, "edge_account_tenant_id": "9188040d-6c67-4c5b-b112-36a304b66dad", "edge_account_type": 1, "edge_tenant_supports_msa_linking": false, "edge_wam_aad_for_app_account_type": 0, "email": "<EMAIL>", "full_name": "", "gaia": "00037FFE1E52532C", "given_name": "", "hd": "", "is_supervised_child": -1, "is_under_advanced_protection": false, "last_downloaded_image_url_with_size": "", "locale": "", "picture_url": ""}], "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "arbitration_last_notification_shown": "*****************", "arbitration_using_experiment_config": false, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"credit_card_enabled": true, "last_version_deduped": 138, "upload_encoding_seed": "51C8513F2AD7A15FBD9C1C5A2C33FD4D"}, "bookmark": {"storage_computation_last_update": "*****************"}, "bookmark_bar": {"show_on_all_tabs": true, "show_only_on_ntp": false}, "browser": {"available_dark_theme_options": "All", "chat_v2": {"ip_eligibility_status": {"last_checked_time": "*****************"}}, "edge_sidebar_visibility": {"_game_assist_": {"order": {"4a4878b3-89d5-4dab-8196-4b88da4a3a76": **********, "********-9c75-4e8b-89fd-ccc06faa85ad": **********, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": **********, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": **********}}, "add_app_to_bottom": true, "order": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": 477218588}}, "edge_sidebar_visibility_debug": {"order_list": ["Search"], "order_raw_data": {"8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"name": "Search", "pos": "477218588"}}}, "editor_proofing_languages": {"en": {"Grammar": false, "Spelling": false}, "en-US": {"Grammar": true, "Spelling": true}}, "enable_text_prediction_v2": true, "gamer_mode_asset_store_prefs": {"779d97ed-2254-4943-a1f3-c811fa709092": {"gamer_mode_modal_script_hash": "xie40asvhdbPXzggtqUJ4lfglpLAYbJeXpWhq51+U+s=", "gamer_mode_modal_script_url": "https://edgeassetservice.azureedge.net/assets/gamer_mode_modal_ux/1.1.69/asset?assetgroup=GamerModeModalUX"}}, "has_seen_welcome_page": false, "hub_app_non_synced_preferences": {"apps": {"06be1ebe-f23a-4bea-ae45-3120ad86cfea": {"last_path": ""}, "0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": {"last_path": ""}, "168a2510-04d5-473e-b6a0-828815a7ca5f": {"last_path": ""}, "1ec8a5a9-971c-4c82-a104-5e1a259456b8": {"last_path": ""}, "2354565a-f412-4654-b89c-f92eaa9dbd20": {"last_path": ""}, "25fe2d1d-e934-482a-a62f-ea1705db905d": {"last_path": ""}, "2caf0cf4-ea42-4083-b928-29b39da1182b": {"last_path": ""}, "35a43603-bb38-4b53-ba20-932cb9117794": {"last_path": ""}, "380c71d3-10bf-4a5d-9a06-c932e4b7d1d8": {"last_path": ""}, "439642fc-998d-4a64-8bb6-940ecaf6b60b": {"last_path": ""}, "4a4878b3-89d5-4dab-8196-4b88da4a3a76": {"last_path": ""}, "523b5ef3-0b10-4154-8b62-10b2ebd00921": {"last_path": ""}, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": {"last_path": ""}, "********-9c75-4e8b-89fd-ccc06faa85ad": {"last_path": ""}, "698b01b4-557a-4a3b-9af7-a7e8138e8372": {"last_path": ""}, "76b926d6-3738-46bf-82d7-2ab896ddf70b": {"last_path": ""}, "7b52ae05-ae84-4165-b083-98ba2031bc22": {"last_path": ""}, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": {"last_path": ""}, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"last_path": ""}, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": {"last_path": ""}, "96defd79-4015-4a32-bd09-794ff72183ef": {"last_path": ""}, "a1a78183-6db3-4789-9e7c-84d157846d55": {"last_path": ""}, "bacc3c12-ebff-44b4-a0f8-ce8b69c9e047": {"last_path": ""}, "c814ae4d-fa0a-4280-a444-cb8bd264828b": {"last_path": ""}, "cd4688a9-e888-48ea-ad81-76193d56b1be": {"last_path": ""}, "d3ff4c56-a2b8-4673-ad13-35e7706cc9d1": {"last_path": ""}, "da15ec1d-543d-41c9-94b8-eb2bd060f2c7": {"last_path": ""}, "dadd1f1c-380c-4871-9e09-7971b6b15069": {"last_path": ""}, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": {"last_path": ""}}}, "hub_app_preferences": {"2cb2db96-3bd0-403e-abe2-9269b3761041": {"auto_show": {"enabled": true}}, "439642fc-998d-4a64-8bb6-940ecaf6b60b": {"auto_show": {"enabled": true}}, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": {"all_scenarios": {"auto_open": {"enabled": false}}, "auto_show": {"enabled": false}}, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"auto_show": {"enabled": true}}, "_game_assist_": {"user_generated_index": ["********-9c75-4e8b-89fd-ccc06faa85ad", "4a4878b3-89d5-4dab-8196-4b88da4a3a76"]}, "cd4688a9-e888-48ea-ad81-76193d56b1be": {"auto_show": {"enabled": true}}, "default_on_apps_cleanup_state": 1, "game_assist_apps_initialized": true, "user_generated": {"4a4878b3-89d5-4dab-8196-4b88da4a3a76": {"device_emulation": "none", "icon_url": "https://static.edge.microsoftapp.net/consumer/edgeml/sai/SAI_favicon_v2/twitch.tv.png", "id": "4a4878b3-89d5-4dab-8196-4b88da4a3a76", "name": "Twitch", "navigable": false, "notificationsEnabled": true, "preferred_side_pane_width": 560, "url": "https://www.twitch.tv/"}, "********-9c75-4e8b-89fd-ccc06faa85ad": {"device_emulation": "none", "icon_url": "https://static.edge.microsoftapp.net/consumer/edgeml/sai/SAI_favicon_v2/discord.com.png", "id": "********-9c75-4e8b-89fd-ccc06faa85ad", "name": "Discord", "navigable": false, "notificationsEnabled": true, "preferred_side_pane_width": 560, "url": "https://discord.com/"}}}, "hub_app_usage_preferences": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": 3, "523b5ef3-0b10-4154-8b62-10b2ebd00921": 3, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": 9, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": 1, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 1, "96defd79-4015-4a32-bd09-794ff72183ef": 4, "9ce3c9c2-462f-4cc9-bbd7-57d656445be0": 1, "OpenFirstTime": 1691247537, "cd4688a9-e888-48ea-ad81-76193d56b1be": 25}, "hub_cleanup_candidate_list_for_debug": [{"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}, {"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}], "hub_cleanup_context": {"cleanup_last_time_v3": 1731884171.89787}, "hub_cleanup_context_v2": {"cleanup_debug_info_v2_adjusted_engaged_app_count": 0, "cleanup_debug_info_v2_app_count_threshold": 1, "cleanup_debug_info_v2_current_sidebar_visibility": 0, "cleanup_debug_info_v2_discover_icon_enabled": true, "cleanup_debug_info_v2_dwell_time_in_secs": 10, "cleanup_debug_info_v2_engaged_app_count": 0, "cleanup_debug_info_v2_expected_sidebar_visibility": 0, "cleanup_debug_info_v2_is_tower_off_by_user": false, "cleanup_debug_info_v2_skip_user_generated_apps_for_threshold": true, "cleanup_debug_info_v2_user_generated_app_count": 0, "hub_app_cleanup_v2_done": true}, "recent_theme_color_list": [4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0], "show_edge_split_window_toolbar_button": false, "show_hub_app_in_sidebar_buttons": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": 3, "2354565a-f412-4654-b89c-f92eaa9dbd20": 0, "523b5ef3-0b10-4154-8b62-10b2ebd00921": 3, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": 3, "76b926d6-3738-46bf-82d7-2ab896ddf70b": 3, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": 3, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": 3, "96defd79-4015-4a32-bd09-794ff72183ef": 3, "9ce3c9c2-462f-4cc9-bbd7-57d656445be0": 3, "_game_assist_": {"4a4878b3-89d5-4dab-8196-4b88da4a3a76": 2, "********-9c75-4e8b-89fd-ccc06faa85ad": 2, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": 2}, "cd4688a9-e888-48ea-ad81-76193d56b1be": 0, "dadd1f1c-380c-4871-9e09-7971b6b15069": 3}, "show_hub_app_in_sidebar_buttons_legacy": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": 3, "2354565a-f412-4654-b89c-f92eaa9dbd20": 0, "523b5ef3-0b10-4154-8b62-10b2ebd00921": 3, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": 3, "76b926d6-3738-46bf-82d7-2ab896ddf70b": 3, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": 3, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": 3, "96defd79-4015-4a32-bd09-794ff72183ef": 3, "cd4688a9-e888-48ea-ad81-76193d56b1be": 0, "dadd1f1c-380c-4871-9e09-7971b6b15069": 3}, "show_hub_app_in_sidebar_buttons_legacy_update_time": "13396499025176576", "show_hub_apps_tower_pinned": false, "show_toolbar_collections_button": false, "time_of_last_normal_window_close": "13396499749308147", "toolbar_browser_essentials_button_pinned": false, "underside_chat_bing_signed_in_status": false, "underside_chat_consent": 0, "user_level_features_context": {}, "window_placement": {"bottom": 1022, "left": 10, "maximized": false, "right": 526, "top": 10, "work_area_bottom": 1032, "work_area_left": 0, "work_area_right": 1920, "work_area_top": 0}}, "browser_content_container_height": 923, "browser_content_container_width": 500, "browser_content_container_x": 0, "browser_content_container_y": 81, "browser_essentials": {"show_hub_fre": false, "show_safety_fre": false}, "collections": {"prism_collection": {"enroll": {"rule_version": 1, "state": 2}}, "prism_collections": {"enabled": 0, "migration": {"accepted": true, "completed": 2, "item_count": 0}, "policy": {"cached": 0}, "wns": {"last_subscribe_time": "13396496599530750", "subscription_id": "1;7994666246405985403"}}}, "commerce_daily_metrics_last_update_time": "13396496564535192", "continuous_migration": {"active_guid": "217163bc-cbf5-4cf1-a6ad-49ed4ee92bb2", "equal_opt_out_users_data": {"backfilled": true}}, "copilot_vision": {"user_access": true}, "countryid_at_install": 18769, "credentials_enable_autosave": true, "credentials_enable_breachdetection": true, "credentials_enable_service": true, "custom_links": {"list": [{"isMostVisited": false, "policyLevel": 0, "position": 0, "title": "Google", "url": "https://www.google.com/"}, {"isMostVisited": false, "policyLevel": 0, "position": 1, "title": "YouTube", "url": "https://www.youtube.com/"}, {"isMostVisited": false, "policyLevel": 0, "position": 2, "title": "الإضافات", "url": "https://chrome.google.com/webstore?utm_source=chrome-ntp-icon"}, {"isMostVisited": false, "policyLevel": 0, "position": 3, "title": "تسجيل الدخول", "url": "https://accounts.google.com/signin/chrome/sync/identifier?ssp=1&continue=https%3A%2F%2Fwww.google.com%2F&flowName=GlifDesktopChromeSync"}]}, "devtools": {"preferences": {"EdgeDevToolsLayoutInfo": {"current_dock_state": 0, "horizontal_size": 300, "showEmulationMode": false, "vertical_size": 555}}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "dual_engine": {"consumer_mode": {"enabled_state": 1}, "consumer_site_list_with_ie_entries": false, "consumer_sitelist_location": "https://go.microsoft.com/fwlink/?linkid=2133855&bucket=37", "consumer_sitelist_version": "97", "external_consumer_shared_cookie_data": {}, "profile_id": "3VBD2TDC", "shared_cookie_data": {}, "sitelist_has_consumer_data": true, "sitelist_has_enterprise_data": false, "sitelist_location": "", "sitelist_source": 0, "sitelist_version": ""}, "edge": {"account_type": 1, "bookmarks": {"last_dup_info_record_time": "*****************"}, "msa_sso_info": {"allow_for_non_msa_profile": false}, "profile_matches_os_primary_account": false, "profile_sso_info": {"aad_sso_algo_state": 1, "is_first_profile": true, "is_msa_first_profile": true, "msa_sso_algo_state": 2, "msa_sso_state_reached_by": 3}, "profile_sso_option": 1, "services": {"last_gaia_id": "00037FFE1E52532C", "signin_scoped_device_id": "b84fb40b-7298-4804-87da-c4753275a81d"}, "workspaces": {"state": "{\"edgeWorkspacePrefsVersion\":2,\"enableFluid\":true,\"failedRestartSpaceId\":\"\",\"failedToConnectToFluid\":false,\"fluidStatus\":0,\"fre_shown\":false,\"fromCache\":false,\"isFluidPreferencesConnected\":false,\"isSpaceOpening\":false,\"openingSpaceId\":\"\",\"statusForScreenReaders\":\"\",\"workspacePopupMode\":0,\"workspacesForExternalLinks\":[]}", "storage": {"state": "{\"app_folder_path\":\"\",\"container_id\":\"\",\"drive_id\":\"\",\"prefs_item_id\":\"\",\"storage_endpoint\":\"\",\"version\":3}"}}}, "edge_cloud_messaging": {"cached_target_token": {"cv": "995132906953252864", "target_token": "hGhbHnXO3m0bwa8cRNdb6g==$Af15jpdQSmSCEXZCo2qQLgQn7w3XFykh8CqmyuoSSrdhtSguI+r0Kf6zkddbSUA13LpvWVFxM2xiOQLyI/XVb4R1ZwEy0tKO6xrxQpvVSWd9XVYimp0YVPSyY3tOHn+u8PDprE2zJ3k4sKSjlSJnixUWtl3i2NhDoDkSX3PuP7he3fie2KnTu61kfe9TMhWA", "time": "13396496576480575"}}, "edge_rewards": {"cache_data": "CAEQrAYYAEIMMDAwNDk5MDYzMDAwSgJpcQ==", "hva_promotions": [], "promotions": [{"attributes": {"State": "<PERSON><PERSON><PERSON>", "activityprogress": "0", "animated_icon": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Icons/newEdgeLogo.json", "bg_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_small.png", "complete": "False", "description": "Search here for 3 days and earn an extra 3,100 points.", "destination": "", "edgebar_description": "", "edgebar_disclaimer": "Offer valid for 1 person/account within 7 days of joining the challenge", "edgebar_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Microsoft_giftcard_grey.png", "edgebar_link_text": "Get started", "edgebar_title": "Welcome to search bar powered by Microsoft Edge! Get a free gift card when you search here for 3 days.", "edgebar_type": "eligible", "give_eligible": "False", "icon": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Icons/newEdgeLogo.json", "image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/278x180/Star-magenta-278x180px.png", "link_text": "Get started", "max": "0", "offerid": "eligibility_EdgeBarMicrosoft_202211_ML293H", "progress": "0", "promotional": "0", "sc_bg_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_medium.png", "sc_bg_large_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_large.png", "small_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Mobile/newEdgeLogo_75x75.png", "title": "Earn bonus Microsoft Rewards points", "type": "url<PERSON><PERSON>"}, "name": "ARIQ_eligibility_EdgeBarMicrosoft_202211_ML293H_info", "priority": -1, "tags": ["exclude_give_pcparent", "non_global_config"]}], "refresh_status_muted_until": "13397101364332644"}, "edge_ux_config": {"assignmentcontext": "arSviywdjnoK/iS9w8H8JSl+JbDTOLei0yySxl+zXQI=", "dataversion": "254488010", "experimentvariables": {"2f717976": {"edgeServerUX.sync.historyDataTypeEnabled": true}, "shopppdismisstreatment": {"edgeServerUX.shopping.msEdgeShoppingCashbackDismissTimeout2s": true}, "shoprevenuattributionc": {"edgeServerUX.shopping.disableCashbackOnCouponCopy": false}}, "flights": {"2f717976": "31213786", "shopppdismisstreatment": "31004791", "shoprevenuattributionc": "31235886"}, "latestcorrelationid": "Ref A: 1B374EC3B90C408DB999E069050C36CB Ref B: FRA31EDGE0710 Ref C: 2025-07-09T01:41:44Z"}, "edge_wallet": {"passwords": {"latest_password_management_count": {"2025-06-12": 1}, "latest_password_usage_count": {"2025-04-05": 1}, "password_lost_report_date": "13396496594350989"}, "trigger_funnel": {"records": []}}, "enable_do_not_track": true, "enterprise_profile_guid": "537ebdc7-b745-4602-9e40-a7a3d3f605b2", "extension": {"installed_extension_count": 9}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "138.0.3351.65", "pdf_upsell_triggered": false, "pinned_extension_migration": true, "pinned_extensions": [], "ui": {"allow_chrome_webstore": true}}, "family_safety": {"activity_reporting_enabled": false, "web_filtering_enabled": false}, "fsd": {"retention_policy_last_version": 138}, "google": {"services": {"consented_to_sync": true, "signin": {"LAST_SIGNIN_ACCESS_POINT": {"time": "2025-07-09T01:02:45.499Z", "value": "17"}}}}, "history": {"thumbnail_visibility": true, "thumbnail_visibility_per_usage": true}, "https_upgrade_navigations": {"2025-07-09": 40}, "import_items_failure_state": {"reimport": {"ie_react": 62436}}, "intl": {"accept_languages": "en-US,en", "selected_languages": "en-US,en"}, "local_browser_data_share": {"index_last_cleaned_time": "13396496624635540", "pin_recommendations_eligible": false}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "hYtU7xyz35Dyel6xCPBVIschXz+mKNoN/hPkyhgMzXgaNIPFm0kGvLBXzciMikxVu/VxzycmhRFZu/FFZFyasw=="}, "muid": {"last_sync": "13396496564532641", "values_seen": ["010DDABDC5E4676700AACC99C44866CB", "024EA44F157A698F1592B26B147A6876", "0742A85569E26065267ABE7168696113"]}, "ntp": {"background_image_type": "imageAndVideo", "layout_mode": 2, "news_feed_display": "always", "next_site_suggestions_available": false, "num_personal_suggestions": 5, "quick_links_options": 0, "record_user_choices": [{"setting": "ntps", "source": "ntp", "timestamp": 1744192382077.0, "value": "{\"m\":\"ar-ae\"}"}, {"setting": "tscollapsed", "source": "tscollapsed_to_off", "timestamp": 1695760253887.0, "value": 0}, {"setting": "breaking_news_dismissed", "source": "ntp", "timestamp": 1749723840137.0, "value": {}}], "show_greeting": true}, "nurturing": {"time_of_last_sync_consent_view": "13396496565511808"}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_profile_store_login_database": true, "profile_store_migrated_to_os_crypt_async": true}, "personalization_data_consent": {"how_set": 7, "personalization_in_context_consent_can_prompt": false, "personalization_in_context_count": 0, "personalization_in_context_has_prompted": false, "when_set": "13313970842049534"}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 20, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "clear_browsing_data_cookies_exceptions": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "13396498905214190", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "edge_ad_targeting": {}, "edge_ad_targeting_data": {}, "edge_sdsm": {}, "edge_split_screen": {}, "edge_u2f_api_request": {}, "edge_user_agent_token": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {"http://localhost:55939,*": {"last_modified": "13396498322273765", "last_visit": "13396320000000000", "setting": 1}}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://localhost:54131,*": {"expiration": "13404273961067824", "last_modified": "13396497961067828", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:55939,*": {"expiration": "13404274436901469", "last_modified": "13396498436901473", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:56702,*": {"expiration": "13404275749306037", "last_modified": "13396499749306042", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {"http://localhost:56702,*": {"last_modified": "13396499749306505", "setting": {"Geolocation": {"ignore_count": 1}}}}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "secure_network": {}, "secure_network_sites": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"http://localhost:54131,*": {"last_modified": "13396497958304689", "setting": {"lastEngagementTime": 1.3396497958304674e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 10.799999999999997, "rawScore": 10.799999999999997}}, "http://localhost:55939,*": {"last_modified": "13396498347240656", "setting": {"lastEngagementTime": 1.3396498347240644e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 7.799999999999998, "rawScore": 7.799999999999998}}, "http://localhost:56702,*": {"last_modified": "13396499214778560", "setting": {"lastEngagementTime": 1.3396499214778548e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 8.399999999999999, "rawScore": 8.399999999999999}}}, "sleeping_tabs": {}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "tech_scam_detection": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "trackers_data": {}, "tracking_org_exceptions": {}, "tracking_org_relationships": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "permission_actions": {"geolocation": [{"action": 0, "prompt_disposition": 1, "time": "13396498322275580"}, {"action": 3, "prompt_disposition": 1, "time": "13396499749306483"}]}, "pref_version": 1}, "created_by_version": "138.0.3351.65", "creation_time": "13396496564297829", "edge_crash_exit_count": 0, "edge_password_is_using_new_login_db_path": false, "edge_password_login_db_path_flip_flop_count": 0, "edge_profile_id": "14e0758e-d0f2-42b6-8fa2-4ef71180ee28", "edge_user_with_non_zero_passwords": true, "exit_type": "Normal", "hard_yes_auto_save_consent": true, "has_seen_signin_fre": false, "is_relative_to_aad": true, "last_engagement_time": "13396499214778548", "last_time_obsolete_http_credentials_removed": 1752023024.346187, "last_time_password_store_metrics_reported": 1752022994.350457, "managed_user_id": "", "name": "Profile 1", "network_pbs": {"ca2ea5f2": {"last_updated": "13395709521250018", "pb": 6}}, "observed_session_time": {"feedback_rating_in_product_help_observed_session_time_key_138.0.3351.65": 878.0}, "one_time_permission_prompts_decided_count": 2, "password_breach_last_scanned_time": "13394197491916570", "password_breach_scan_trigger_last_reset_time": "13386502670228157", "password_breach_scan_triggered_count": 0, "password_breach_scan_triggered_password_count": 0, "password_breach_scanned": true, "password_hash_data_list": [], "signin_fre_seen_time": "13396496564323369", "using_default_avatar": false, "using_gaia_avatar": true, "were_old_google_logins_removed": true}, "read_aloud": {"last_used_time": "13286840559239476"}, "reset_prepopulated_engines": false, "safebrowsing": {"advanced_protection_last_refresh": "13396498905127459", "extension_telemetry_file_data": {}}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "sessions": {"event_log": [{"crashed": false, "time": "13396496564344183", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396497961062873", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396498175538640", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "133964***********", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}], "session_data_status": 3}, "shopping": {"contextual_features_enabled": true, "dma_telemetry_expiration_time": "*****************", "last_pwilo_api_fetch_time": "*****************", "pcb_supported": true}, "should_read_incoming_syncing_theme_prefs": false, "signin": {"accounts_metadata_dict": {"00037FFE1E52532C": {"BookmarksExplicitBrowserSigninEnabled": false, "ExtensionsExplicitBrowserSigninEnabled": false}}, "allowed": true, "cookie_clear_on_exit_migration_notice_complete": true, "signin_with_explicit_browser_signin_on": true, "sync_paused_start_time": "*****************"}, "smart_explore": {"auto_cleanup": {"check_time": "*****************", "noengagement_since": "*****************"}, "auto_cleanup_date": "*****************"}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "surf_game": {"buoy_highscore": -1, "classic_highscore": 0, "speed_highscore": -1}, "sync": {"apps": true, "autofill": true, "bookmarks": true, "cached_passphrase_type": 2, "cached_persistent_auth_error": false, "cached_trusted_vault_auto_upgrade_experiment_group": "", "collections": true, "collections_edge_re_evaluated": true, "collections_edge_supported": true, "edge_account_type": 1, "edge_wallet": true, "edge_wallet_edge_supported": true, "encryption_bootstrap_token_per_account_migration_done": true, "extensions": true, "extensions_edge_supported": true, "first_full_sync_completed": true, "gaia_id": "00037FFE1E52532C", "has_been_enabled": true, "has_setup_completed": true, "history_edge_supported": true, "keep_everything_synced": true, "keystore_encryption_key_state": "****************************************************************************************************************************************************************************************************************************************************************************************", "local_device_guids_with_timestamp": [{"cache_guid": "pLTlnEc1hqBPtd4BY2cj/Q==", "timestamp": 155052}], "passwords": true, "passwords_per_account_pref_migration_done": true, "preferences": true, "tabs": true, "tabs_edge_supported": true, "transport_data_per_account": {"wMUaF/ED0dCJFqFIFkN/fGAtxQE2NhsHNKWrgUMPvt4=": {"sync.bag_of_chips": "", "sync.birthday": "ProductionEnvironmentDefinition", "sync.cache_guid": "pLTlnEc1hqBPtd4BY2cj/Q==", "sync.last_poll_time": "*****************", "sync.last_synced_time": "*****************", "sync.short_poll_interval": "***********"}}, "typed_urls": true}, "sync_consent_recorded": true, "sync_profile_info": {"edge_ci_consent_last_modified_date": "*****************", "edge_ci_consent_last_shown_date": "*****************", "edge_ci_is_option_explicitly_selectedby_user": false}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "tab_groups": [], "tab_groups_migration_version": 3, "third_party_search": {"consented": true}, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "toolbar_declutter": {"new_user_cleanup_triggered": true, "undo": {"last_time": "*****************"}}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 49, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "user_experience_metrics": {"personalization_data_consent_enabled": true, "personalization_data_consent_enabled_last_known_value": true, "reporting_personalization_enabled": true}, "video_enhancement": {"mode": "Non-AI enhancement"}, "visual_search": {"dma_state": 1}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138", "link_handling_info": {"enabled_for_installed_apps": true}}}