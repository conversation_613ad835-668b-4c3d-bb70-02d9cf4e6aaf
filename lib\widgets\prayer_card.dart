import 'package:flutter/material.dart';
import 'package:animations/animations.dart';
import '../generated/l10n.dart';
import '../models/prayer_model.dart';
import '../theme/app_theme.dart';

class PrayerCard extends StatefulWidget {
  final PrayerModel prayer;
  final Function(PrayerStatus) onStatusChanged;

  const PrayerCard({
    Key? key,
    required this.prayer,
    required this.onStatusChanged,
  }) : super(key: key);

  @override
  State<PrayerCard> createState() => _PrayerCardState();
}

class _PrayerCardState extends State<PrayerCard> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _colorAnimation = ColorTween(
      begin: _getStatusColor(),
      end: _getStatusColor().withOpacity(0.8),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Color _getStatusColor() {
    switch (widget.prayer.status) {
      case PrayerStatus.completed:
        return AppTheme.primaryGreen;
      case PrayerStatus.onTime:
        return AppTheme.primaryBlue;
      case PrayerStatus.late:
        return AppTheme.primaryOrange;
      case PrayerStatus.missed:
        return AppTheme.primaryRed;
    }
  }

  IconData _getStatusIcon() {
    switch (widget.prayer.status) {
      case PrayerStatus.completed:
        return Icons.check_circle;
      case PrayerStatus.onTime:
        return Icons.access_time;
      case PrayerStatus.late:
        return Icons.schedule;
      case PrayerStatus.missed:
        return Icons.cancel;
    }
  }

  String _getPrayerName(S localizations) {
    switch (widget.prayer.type) {
      case PrayerType.fajr:
        return localizations.fajr;
      case PrayerType.dhuhr:
        return localizations.dhuhr;
      case PrayerType.asr:
        return localizations.asr;
      case PrayerType.maghrib:
        return localizations.maghrib;
      case PrayerType.isha:
        return localizations.isha;
    }
  }

  String _getStatusText(S localizations) {
    switch (widget.prayer.status) {
      case PrayerStatus.completed:
        return localizations.completed;
      case PrayerStatus.onTime:
        return localizations.onTime;
      case PrayerStatus.late:
        return localizations.late;
      case PrayerStatus.missed:
        return localizations.missed;
    }
  }

  void _showStatusDialog() {
    showModal(
      context: context,
      builder: (context) => _StatusSelectionDialog(
        currentStatus: widget.prayer.status,
        onStatusSelected: (status) {
          widget.onStatusChanged(status);
          _animationController.forward().then((_) {
            _animationController.reverse();
          });
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = S.of(context);
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTap: _showStatusDialog,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: _colorAnimation.value ?? _getStatusColor(),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: (_colorAnimation.value ?? _getStatusColor()).withOpacity(0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Prayer Icon
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: _getStatusColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.mosque,
                      color: _getStatusColor(),
                      size: 24,
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // Prayer Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _getPrayerName(localizations),
                          style: theme.textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _getStatusText(localizations),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: _getStatusColor(),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Status Icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getStatusColor(),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getStatusIcon(),
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class _StatusSelectionDialog extends StatelessWidget {
  final PrayerStatus currentStatus;
  final Function(PrayerStatus) onStatusSelected;

  const _StatusSelectionDialog({
    required this.currentStatus,
    required this.onStatusSelected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = S.of(context);
    
    return AlertDialog(
      title: Text('تغيير حالة الصلاة'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: PrayerStatus.values.map((status) {
          final isSelected = status == currentStatus;
          return ListTile(
            leading: Icon(
              _getStatusIcon(status),
              color: _getStatusColor(status),
            ),
            title: Text(_getStatusText(status, localizations)),
            trailing: isSelected ? const Icon(Icons.check) : null,
            onTap: () {
              onStatusSelected(status);
              Navigator.of(context).pop();
            },
          );
        }).toList(),
      ),
    );
  }

  Color _getStatusColor(PrayerStatus status) {
    switch (status) {
      case PrayerStatus.completed:
        return AppTheme.primaryGreen;
      case PrayerStatus.onTime:
        return AppTheme.primaryBlue;
      case PrayerStatus.late:
        return AppTheme.primaryOrange;
      case PrayerStatus.missed:
        return AppTheme.primaryRed;
    }
  }

  IconData _getStatusIcon(PrayerStatus status) {
    switch (status) {
      case PrayerStatus.completed:
        return Icons.check_circle;
      case PrayerStatus.onTime:
        return Icons.access_time;
      case PrayerStatus.late:
        return Icons.schedule;
      case PrayerStatus.missed:
        return Icons.cancel;
    }
  }

  String _getStatusText(PrayerStatus status, S localizations) {
    switch (status) {
      case PrayerStatus.completed:
        return localizations.completed;
      case PrayerStatus.onTime:
        return localizations.onTime;
      case PrayerStatus.late:
        return localizations.late;
      case PrayerStatus.missed:
        return localizations.missed;
    }
  }
}
