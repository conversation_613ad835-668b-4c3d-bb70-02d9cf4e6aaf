import 'package:flutter/material.dart';
import 'package:animations/animations.dart';
import '../generated/l10n.dart';
import '../theme/app_theme.dart';
import '../models/prayer_model.dart';
import '../widgets/progress_chart.dart';
import '../widgets/weekly_progress_card.dart';
import '../widgets/monthly_stats_card.dart';

class PrayerProgressScreen extends StatefulWidget {
  const PrayerProgressScreen({Key? key}) : super(key: key);

  @override
  State<PrayerProgressScreen> createState() => _PrayerProgressScreenState();
}

class _PrayerProgressScreenState extends State<PrayerProgressScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<Animation<double>> _cardAnimations;
  
  int _selectedPeriod = 0; // 0: Week, 1: Month, 2: All Time
  
  // Mock data - في التطبيق الحقيقي ستأتي من قاعدة البيانات
  final Map<String, int> _weeklyStats = {
    'completed': 28,
    'missed': 7,
    'total': 35,
  };
  
  final Map<String, int> _monthlyStats = {
    'completed': 120,
    'missed': 30,
    'total': 150,
  };

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _cardAnimations = List.generate(
      4,
      (index) => Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          index * 0.1,
          0.6 + (index * 0.1),
          curve: Curves.easeOut,
        ),
      )),
    );
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = S.of(context);
    
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          await Future.delayed(const Duration(seconds: 1));
          _animationController.reset();
          _animationController.forward();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Period Selector
              FadeTransition(
                opacity: _cardAnimations[0],
                child: SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0, -0.3),
                    end: Offset.zero,
                  ).animate(_cardAnimations[0]),
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: theme.cardColor,
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        _buildPeriodButton(localizations.thisWeek, 0),
                        _buildPeriodButton(localizations.thisMonth, 1),
                        _buildPeriodButton(localizations.allTime, 2),
                      ],
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Progress Overview
              FadeTransition(
                opacity: _cardAnimations[1],
                child: SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(-0.3, 0),
                    end: Offset.zero,
                  ).animate(_cardAnimations[1]),
                  child: _buildProgressOverview(),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Weekly Progress Chart
              FadeTransition(
                opacity: _cardAnimations[2],
                child: SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0.3, 0),
                    end: Offset.zero,
                  ).animate(_cardAnimations[2]),
                  child: const WeeklyProgressCard(),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Monthly Stats
              FadeTransition(
                opacity: _cardAnimations[3],
                child: SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0, 0.3),
                    end: Offset.zero,
                  ).animate(_cardAnimations[3]),
                  child: const MonthlyStatsCard(),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Progress Chart
              FadeTransition(
                opacity: _cardAnimations[3],
                child: const ProgressChart(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPeriodButton(String title, int index) {
    final isSelected = _selectedPeriod == index;
    
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedPeriod = index;
          });
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected ? AppTheme.primaryGreen : Colors.transparent,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isSelected ? Colors.white : Colors.grey[600],
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProgressOverview() {
    final stats = _selectedPeriod == 0 ? _weeklyStats : _monthlyStats;
    final completionRate = (stats['completed']! / stats['total']!) * 100;
    
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryGreen,
            AppTheme.primaryGreen.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryGreen.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // Completion Rate
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${completionRate.round()}%',
                style: const TextStyle(
                  fontSize: 48,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          Text(
            'معدل الإنجاز',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withOpacity(0.9),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Stats Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                '${stats['completed']}',
                'مكتملة',
                Icons.check_circle,
              ),
              _buildStatItem(
                '${stats['missed']}',
                'فائتة',
                Icons.cancel,
              ),
              _buildStatItem(
                '${stats['total']}',
                'المجموع',
                Icons.mosque,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String value, String label, IconData icon) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: Colors.white,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.white.withOpacity(0.9),
          ),
        ),
      ],
    );
  }
}
