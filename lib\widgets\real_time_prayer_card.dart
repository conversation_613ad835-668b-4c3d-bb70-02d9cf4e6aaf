import 'package:flutter/material.dart';

import '../generated/l10n.dart';
import '../theme/app_theme.dart';
import '../models/prayer_times_model.dart';
import '../services/prayer_times_service.dart';
import '../services/prayer_data_service.dart';

/// بطاقة الصلوات الحقيقية مع أوقات الصلاة من API
class RealTimePrayerCard extends StatefulWidget {
  const RealTimePrayerCard({super.key});

  @override
  State<RealTimePrayerCard> createState() => _RealTimePrayerCardState();
}

class _RealTimePrayerCardState extends State<RealTimePrayerCard>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<Animation<double>> _cardAnimations;

  PrayerTimesModel? _prayerTimes;
  DailyPrayerStatus? _todayStatus;
  bool _isLoading = true;
  String _hijriDate = '';

  // أسماء الصلوات بالعربية
  final List<Map<String, dynamic>> _prayers = [
    {
      'key': 'fajr',
      'name': 'الفجر',
      'icon': Icons.wb_twilight,
      'color': AppTheme.primaryBlue
    },
    {
      'key': 'dhuhr',
      'name': 'الظهر',
      'icon': Icons.wb_sunny,
      'color': AppTheme.primaryOrange
    },
    {
      'key': 'asr',
      'name': 'العصر',
      'icon': Icons.wb_sunny_outlined,
      'color': AppTheme.primaryGreen
    },
    {
      'key': 'maghrib',
      'name': 'المغرب',
      'icon': Icons.wb_twilight,
      'color': AppTheme.primaryPurple
    },
    {
      'key': 'isha',
      'name': 'العشاء',
      'icon': Icons.nightlight_round,
      'color': AppTheme.primaryRed
    },
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _cardAnimations = List.generate(
      5,
      (index) => Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          index * 0.1,
          0.6 + (index * 0.1),
          curve: Curves.easeOut,
        ),
      )),
    );

    _loadPrayerData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// تحميل بيانات الصلاة من API والتخزين المحلي
  Future<void> _loadPrayerData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // جلب أوقات الصلاة من API
      final prayerTimes =
          await PrayerTimesService.getPrayerTimesForCurrentLocation();

      // جلب التاريخ الميلادي (مبسط)
      final now = DateTime.now();
      final hijriDate = '${now.day} ${_getMonthName(now.month)} ${now.year} م';

      // جلب حالة صلوات اليوم
      final today = DateTime.now();
      DailyPrayerStatus? todayStatus =
          await PrayerDataService.getDailyPrayerStatus(today);

      // إذا لم توجد بيانات لليوم، إنشاء بيانات جديدة
      if (todayStatus == null) {
        todayStatus = DailyPrayerStatus.newDay(
          date: today,
          hijriDate: hijriDate,
          prayerTimes: prayerTimes,
        );
        await PrayerDataService.saveDailyPrayerStatus(todayStatus);
      }

      setState(() {
        _prayerTimes = prayerTimes;
        _todayStatus = todayStatus;
        _hijriDate = hijriDate;
        _isLoading = false;
      });

      _animationController.forward();
    } catch (e) {
      print('❌ خطأ في تحميل بيانات الصلاة: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// تحديث حالة صلاة معينة
  Future<void> _togglePrayerStatus(String prayerKey) async {
    if (_todayStatus == null) return;

    final currentStatus = _todayStatus!.prayerStatus[prayerKey] ?? false;
    final newStatus = !currentStatus;

    // تحديث الحالة محلياً
    setState(() {
      _todayStatus = _todayStatus!.copyWithPrayerStatus(prayerKey, newStatus);
    });

    // حفظ التحديث في التخزين المحلي
    await PrayerDataService.updatePrayerStatus(
      date: DateTime.now(),
      prayerName: prayerKey,
      completed: newStatus,
    );

    // إظهار رسالة تأكيد
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            newStatus ? '✅ تم تسجيل الصلاة كمكتملة' : '❌ تم إلغاء تسجيل الصلاة',
          ),
          backgroundColor:
              newStatus ? AppTheme.primaryGreen : AppTheme.primaryRed,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  /// الحصول على وقت الصلاة
  String _getPrayerTime(String prayerKey) {
    if (_prayerTimes == null) return '--:--';

    switch (prayerKey) {
      case 'fajr':
        return _prayerTimes!.fajr;
      case 'dhuhr':
        return _prayerTimes!.dhuhr;
      case 'asr':
        // حساب وقت العصر تقريبي
        final asrTime = PrayerTimesService.calculateAsrTime(_prayerTimes!);
        return asrTime?.format(context) ?? '--:--';
      case 'maghrib':
        return _prayerTimes!.maghrib;
      case 'isha':
        // حساب وقت العشاء تقريبي
        final ishaTime = PrayerTimesService.calculateIshaTime(_prayerTimes!);
        return ishaTime?.format(context) ?? '--:--';
      default:
        return '--:--';
    }
  }

  /// الحصول على اسم الشهر بالعربية
  String _getMonthName(int month) {
    const months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر'
    ];
    return months[month - 1];
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(20),
        ),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: AppTheme.primary.withOpacity(0.08),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان والتاريخ الهجري
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'صلوات اليوم',
                style: theme.textTheme.displaySmall?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppTheme.onSurface,
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppTheme.primaryGreen.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _hijriDate,
                  style: const TextStyle(
                    color: AppTheme.primaryGreen,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // قائمة الصلوات
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _prayers.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final prayer = _prayers[index];
              final prayerKey = prayer['key'] as String;
              final isCompleted =
                  _todayStatus?.prayerStatus[prayerKey] ?? false;

              return AnimatedBuilder(
                animation: _cardAnimations[index],
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(
                      (1 - _cardAnimations[index].value) * 100,
                      0,
                    ),
                    child: Opacity(
                      opacity: _cardAnimations[index].value,
                      child: _buildPrayerTile(prayer, isCompleted),
                    ),
                  );
                },
              );
            },
          ),

          const SizedBox(height: 20),

          // إحصائيات سريعة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildQuickStat(
                  'مكتملة',
                  '${_todayStatus?.completedPrayersCount ?? 0}',
                  AppTheme.primaryGreen,
                ),
                _buildQuickStat(
                  'متبقية',
                  '${_todayStatus?.incompletePrayersCount ?? 5}',
                  AppTheme.primaryOrange,
                ),
                _buildQuickStat(
                  'النسبة',
                  '${((_todayStatus?.completedPrayersCount ?? 0) / 5 * 100).round()}%',
                  AppTheme.primaryBlue,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrayerTile(Map<String, dynamic> prayer, bool isCompleted) {
    final prayerKey = prayer['key'] as String;
    final prayerName = prayer['name'] as String;
    final prayerIcon = prayer['icon'] as IconData;
    final prayerColor = prayer['color'] as Color;
    final prayerTime = _getPrayerTime(prayerKey);

    return GestureDetector(
      onTap: () => _togglePrayerStatus(prayerKey),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isCompleted
              ? prayerColor.withOpacity(0.08)
              : AppTheme.surfaceVariant,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color:
                isCompleted ? prayerColor.withOpacity(0.3) : Colors.transparent,
            width: 1.5,
          ),
          boxShadow: isCompleted
              ? [
                  BoxShadow(
                    color: prayerColor.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          children: [
            // أيقونة الصلاة
            Container(
              padding: const EdgeInsets.all(14),
              decoration: BoxDecoration(
                color: isCompleted
                    ? prayerColor.withOpacity(0.15)
                    : prayerColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(14),
              ),
              child: Icon(
                prayerIcon,
                color: prayerColor,
                size: 26,
              ),
            ),

            const SizedBox(width: 16),

            // اسم الصلاة
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    prayerName,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: isCompleted ? prayerColor : AppTheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    prayerTime,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 12),

            // أيقونة الحالة
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: isCompleted ? prayerColor : AppTheme.onSurfaceLight,
                shape: BoxShape.circle,
                boxShadow: isCompleted
                    ? [
                        BoxShadow(
                          color: prayerColor.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
              child: Icon(
                isCompleted ? Icons.check_rounded : Icons.circle_outlined,
                color: Colors.white,
                size: 18,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStat(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
}
