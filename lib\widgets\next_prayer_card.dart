import 'package:flutter/material.dart';
import 'dart:async';
import '../generated/l10n.dart';
import '../theme/app_theme.dart';
import '../models/prayer_model.dart';

class NextPrayerCard extends StatefulWidget {
  const NextPrayerCard({Key? key}) : super(key: key);

  @override
  State<NextPrayerCard> createState() => _NextPrayerCardState();
}

class _NextPrayerCardState extends State<NextPrayerCard>
    with TickerProviderStateMixin {
  late Timer _timer;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  // Mock prayer times - في التطبيق الحقيقي ستأتي من API
  final Map<PrayerType, TimeOfDay> _prayerTimes = {
    PrayerType.fajr: const TimeOfDay(hour: 5, minute: 30),
    PrayerType.dhuhr: const TimeOfDay(hour: 12, minute: 15),
    PrayerType.asr: const TimeOfDay(hour: 15, minute: 45),
    PrayerType.maghrib: const TimeOfDay(hour: 18, minute: 20),
    PrayerType.isha: const TimeOfDay(hour: 19, minute: 45),
  };

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _pulseController.repeat(reverse: true);

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    _pulseController.dispose();
    super.dispose();
  }

  PrayerType _getNextPrayer() {
    final now = TimeOfDay.now();
    final currentMinutes = now.hour * 60 + now.minute;

    for (final entry in _prayerTimes.entries) {
      final prayerMinutes = entry.value.hour * 60 + entry.value.minute;
      if (prayerMinutes > currentMinutes) {
        return entry.key;
      }
    }

    // إذا انتهت صلوات اليوم، الصلاة التالية هي فجر الغد
    return PrayerType.fajr;
  }

  Duration _getTimeUntilNextPrayer() {
    final nextPrayer = _getNextPrayer();
    final nextPrayerTime = _prayerTimes[nextPrayer]!;
    final now = DateTime.now();

    var nextPrayerDateTime = DateTime(
      now.year,
      now.month,
      now.day,
      nextPrayerTime.hour,
      nextPrayerTime.minute,
    );

    // إذا كانت الصلاة التالية في اليوم التالي
    if (nextPrayerDateTime.isBefore(now)) {
      nextPrayerDateTime = nextPrayerDateTime.add(const Duration(days: 1));
    }

    return nextPrayerDateTime.difference(now);
  }

  String _getPrayerName(PrayerType type, S localizations) {
    switch (type) {
      case PrayerType.fajr:
        return localizations.fajr;
      case PrayerType.dhuhr:
        return localizations.dhuhr;
      case PrayerType.asr:
        return localizations.asr;
      case PrayerType.maghrib:
        return localizations.maghrib;
      case PrayerType.isha:
        return localizations.isha;
    }
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = S.of(context);
    final nextPrayer = _getNextPrayer();
    final timeRemaining = _getTimeUntilNextPrayer();

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppTheme.primaryBlue,
                  AppTheme.primaryBlue.withOpacity(0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryBlue.withOpacity(0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.access_time,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            localizations.nextPrayer,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: Colors.white.withOpacity(0.9),
                            ),
                          ),
                          Text(
                            _getPrayerName(nextPrayer, localizations),
                            style: theme.textTheme.headlineMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        localizations.timeRemaining,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                      Text(
                        _formatDuration(timeRemaining),
                        style: theme.textTheme.headlineMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'monospace',
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
